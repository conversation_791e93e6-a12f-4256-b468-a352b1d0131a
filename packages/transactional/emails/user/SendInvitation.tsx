import {
	<PERSON>,
	<PERSON><PERSON>,
	Container,
	Head,
	<PERSON><PERSON>,
	Hr,
	Html,
	Preview,
	Tailwind,
	Text,
} from "@react-email/components";
import { Logo } from "../../components/Logo";

interface InvitationProps {
	inviter: string;
	inviteeEmail: string;
	teamName: string;
	acceptUrl: string;
}

const InvitationEmail = ({
	inviter,
	inviteeEmail,
	teamName,
	acceptUrl,
}: InvitationProps) => {
	const subject = `You're invited to join ${teamName}`;

	return (
		<Html>
			<Head />
			<Preview>{subject}</Preview>
			<Tailwind>
				<Body className="font-sans bg-white">
					<Container className="mx-auto my-10 max-w-[500px] rounded border border-gray-200 p-6">
						<Logo />
						<Heading className="mb-4 text-xl font-semibold">{subject}</Heading>
						<Text>
							<strong>{inviter}</strong> has invited you (
							<strong>{inviteeEmail}</strong>) to join their {teamName} team.
						</Text>

						<Button
							href={acceptUrl}
							className="px-4 py-2 mt-4 text-white bg-blue-600 rounded"
						>
							Accept Invitation
						</Button>

						<Text className="mt-4 text-gray-700">
							If you don’t want to join, you can ignore this email.
						</Text>

						<Hr className="my-6 border-gray-200" />
						<Text className="text-xs text-gray-500">
							This invitation will expire in 7 days.
						</Text>
					</Container>
				</Body>
			</Tailwind>
		</Html>
	);
};

InvitationEmail.PreviewProps = {
	inviter: "Jane Doe",
	inviteeEmail: "<EMAIL>",
	teamName: "SnapBack",
	acceptUrl: "https://yoursite.com/invite/accept/12345",
} as InvitationProps;

export default InvitationEmail;
