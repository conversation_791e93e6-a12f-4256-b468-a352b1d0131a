import { z } from "zod";

import * as zodSchemas from "../../../../apps/server/prisma/generated/zod/schemas/index";

// Create Inputs
// export const CreateAccountInputSchema = z.object({
// 	userId: z.string(),
// });
// export type AccountInput = z.infer<typeof CreateAccountInputSchema>;

export const CreateLicenseInputSchema = zodSchemas.LicenseInputSchema;
export type CreateLicenseInput = z.infer<typeof CreateLicenseInputSchema>;

// export const CreateDeviceInputSchema = zodSchemas.DeviceInputSchema;
// export type CreateDeviceInput = z.infer<typeof CreateDeviceInputSchema>;

// export const CreateUserInputSchema = zodSchemas.UserInputSchema;
// export type CreateUserInput = z.infer<typeof CreateUserInputSchema>;

// export const CreateEventInputSchema = zodSchemas.WebhookEventInputSchema;
// export type CreateEventInput = z.infer<typeof CreateEventInputSchema>;

// export const CreateDeviceExpansionInputSchema =
// 	zodSchemas.DeviceExpansionInputSchema;
// export type CreateDeviceExpansionInput = z.infer<
// 	typeof CreateDeviceExpansionInputSchema
// >;

// export const CreateInvitationInputSchema =
// 	zodSchemas.InvitationInputSchema.pick({
// 		email: true,
// 		role: true,
// 	});
// export type CreateInvitationInput = z.infer<typeof CreateInvitationInputSchema>;

// export const CreateMessageInputSchema = zodSchemas.SupportMessageInputSchema;
// export type CreateMessageInput = z.infer<typeof CreateMessageInputSchema>;

// export const CreatePaymentIntentInputSchema =
// 	zodSchemas.PaymentIntentInputSchema;
// export type CreatePaymentIntentInput = z.infer<
// 	typeof CreatePaymentIntentInputSchema
// >;

// export const CreateRateLimitInputSchema = zodSchemas.RateLimitInputSchema;
// export type CreateRateLimitInput = z.infer<typeof CreateRateLimitInputSchema>;

// // Had to rewrite this one because stripeRefundIds is a string array
// export const CreateRefundRequestInputSchema =
// 	zodSchemas.RefundRequestInputSchema.extend({
// 		stripeRefundIds: z.string().array().optional().default([]),
// 	});
// export type CreateRefundRequestInput = z.infer<
// 	typeof CreateRefundRequestInputSchema
// >;

// export const CreateAuditLogInputSchema = zodSchemas.AuditLogInputSchema;
// export type CreateAuditLogInput = z.infer<typeof CreateAuditLogInputSchema>;

// export const CreateTicketInputSchema = zodSchemas.SupportTicketInputSchema;
// export type CreateTicketInput = z.infer<typeof CreateTicketInputSchema>;

// // Update Inputs

// export const UpdateTicketInputSchema = CreateTicketInputSchema.partial().extend(
// 	{
// 		id: z.string(),
// 	},
// );
// export type UpdateTicketInput = z.infer<typeof UpdateTicketInputSchema>;

// export const UpdateMessageInputSchema =
// 	CreateMessageInputSchema.partial().extend({
// 		id: z.string(),
// 	});
// export type UpdateMessageInput = z.infer<typeof UpdateMessageInputSchema>;

// export const UpdatePaymentIntentInputSchema =
// 	CreatePaymentIntentInputSchema.partial().extend({
// 		id: z.string(),
// 	});
// export type UpdatePaymentIntentInput = z.infer<
// 	typeof UpdatePaymentIntentInputSchema
// >;

// export const UpdateRefundRequestInputSchema =
// 	CreateRefundRequestInputSchema.partial().extend({
// 		id: z.string(),
// 	});
// export type UpdateRefundRequestInput = z.infer<
// 	typeof UpdateRefundRequestInputSchema
// >;

// export const UpdateLicenseInputSchema =
// 	CreateLicenseInputSchema.partial().extend({
// 		id: z.string(),
// 	});

// export type UpdateLicenseInput = z.infer<typeof UpdateLicenseInputSchema>;

// export const UpdateDeviceInputSchema = CreateDeviceInputSchema.partial().extend(
// 	{
// 		id: z.string(),
// 	},
// );
// export type UpdateDeviceInput = z.infer<typeof UpdateDeviceInputSchema>;

// export const UpdateUserInputSchema = CreateUserInputSchema.partial().extend({
// 	id: z.string(),
// });
// export type UpdateUserInput = z.infer<typeof UpdateUserInputSchema>;

// export const UpdateDeviceExpansionInputSchema =
// 	CreateDeviceExpansionInputSchema.partial().extend({
// 		id: z.string(),
// 	});
// export type UpdateDeviceExpansionInput = z.infer<
// 	typeof UpdateDeviceExpansionInputSchema
// >;

// export const UpdateInvitationInputSchema =
// 	zodSchemas.InvitationInputSchema.partial().extend({
// 		id: z.string(),
// 	});
// export type UpdateInvitationInput = z.infer<typeof UpdateInvitationInputSchema>;

// export const UpdateAuditLogInputSchema =
// 	CreateAuditLogInputSchema.partial().extend({
// 		id: z.string(),
// 	});
// export type UpdateAuditLogInput = z.infer<typeof UpdateAuditLogInputSchema>;

// export const UpdateRateLimitInputSchema =
// 	CreateRateLimitInputSchema.partial().extend({
// 		id: z.string(),
// 	});
// export type UpdateRateLimitInput = z.infer<typeof UpdateRateLimitInputSchema>;

// export const UpdateEventInputSchema = CreateEventInputSchema.partial().extend({
// 	id: z.string(),
// });
// export type UpdateEventInput = z.infer<typeof UpdateEventInputSchema>;

// Read and Delete

export const IdInputSchema = z.object({
	id: z.string(),
});
export type IdInput = z.infer<typeof IdInputSchema>;

export const TokenInputSchema = z.object({
	token: z.string(),
});
export type TokenInput = z.infer<typeof TokenInputSchema>;

// Common
export const PaginateInputSchema = z.object({
	page: z.number().int().min(1).optional(),
	limit: z.number().int().min(1).optional(),
	sortBy: z.string().optional(),
	sortOrder: z.enum(["asc", "desc"]).optional(),
	filters: z.record(z.string(), z.string()).optional(),
	search: z.string().optional(),
});
export type PaginateInput = z.infer<typeof PaginateInputSchema>;

export const MetaSchema = z.object({
	totalCount: z.number(),
	totalPages: z.number(),
	page: z.number(),
	limit: z.number(),
	nextPage: z.number().optional(),
	previousPage: z.number().optional(),
});
export type Meta = z.infer<typeof MetaSchema>;

export const FiltersInputSchema = z.object({
	filters: z.record(z.string(), z.string()).optional(),
});

export type FiltersInput = z.infer<typeof FiltersInputSchema>;
