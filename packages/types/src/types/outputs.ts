import type { z } from "zod";
import { AccountModelSchema } from "../../../../apps/server/prisma/generated/zod/schemas/variants/pure/Account.pure";
import { AuditLogModelSchema } from "../../../../apps/server/prisma/generated/zod/schemas/variants/pure/AuditLog.pure";
import { DeviceModelSchema } from "../../../../apps/server/prisma/generated/zod/schemas/variants/pure/Device.pure";
import { DeviceExpansionModelSchema } from "../../../../apps/server/prisma/generated/zod/schemas/variants/pure/DeviceExpansion.pure";
import { InvitationModelSchema } from "../../../../apps/server/prisma/generated/zod/schemas/variants/pure/Invitation.pure";
import { LicenseModelSchema } from "../../../../apps/server/prisma/generated/zod/schemas/variants/pure/License.pure";
import { PaymentIntentModelSchema } from "../../../../apps/server/prisma/generated/zod/schemas/variants/pure/PaymentIntent.pure";
import { RateLimitModelSchema } from "../../../../apps/server/prisma/generated/zod/schemas/variants/pure/RateLimit.pure";
import { RefundRequestModelSchema } from "../../../../apps/server/prisma/generated/zod/schemas/variants/pure/RefundRequest.pure";
import { SessionModelSchema } from "../../../../apps/server/prisma/generated/zod/schemas/variants/pure/Session.pure";
import { SupportMessageModelSchema } from "../../../../apps/server/prisma/generated/zod/schemas/variants/pure/SupportMessage.pure";
import { SupportTicketModelSchema } from "../../../../apps/server/prisma/generated/zod/schemas/variants/pure/SupportTicket.pure";
import { UserModelSchema } from "../../../../apps/server/prisma/generated/zod/schemas/variants/pure/User.pure";
import { WebhookEventModelSchema } from "../../../../apps/server/prisma/generated/zod/schemas/variants/pure/WebhookEvent.pure";
import { createPaginatedOutputSchema } from "./helpers";

// Base Schemas (No relationship data)
export const AccountBaseSchema = AccountModelSchema.omit({
	user: true,
});
export const LicenseBaseSchema = LicenseModelSchema.omit({
	devices: true,
	deviceExpansions: true,
	refundRequests: true,
});

export const DeviceBaseSchema = DeviceModelSchema.omit({
	license: true,
});

export const UserBaseSchema = UserModelSchema.omit({
	sessions: true,
	accounts: true,
	createdLicenses: true,
	sentInvitations: true,
	receivedInvitations: true,
	auditLogsAsActor: true,
	auditLogsAsTarget: true,
	processedRefunds: true,
	assignedTickets: true,
	supportMessages: true,
});

export const EventBaseSchema = WebhookEventModelSchema.omit({
	paymentIntent: true,
});

export const DeviceExpansionBaseSchema = DeviceExpansionModelSchema.omit({
	license: true,
	paymentIntent: true,
});

export const InvitationBaseSchema = InvitationModelSchema.omit({
	sentByUser: true,
	acceptedByUser: true,
});

export const MessageBaseSchema = SupportMessageModelSchema.omit({
	ticket: true,
	authorUser: true,
});

export const PaymentIntentBaseSchema = PaymentIntentModelSchema.omit({
	licenses: true,
	deviceExpansions: true,
	webhookEvents: true,
});

export const RateLimitBaseSchema = RateLimitModelSchema.omit({});

export const RefundRequestBaseSchema = RefundRequestModelSchema.omit({
	license: true,
	processedByUser: true,
});

export const AuditLogBaseSchema = AuditLogModelSchema.omit({
	user: true,
	target: true,
});

export const TicketBaseSchema = SupportTicketModelSchema.omit({
	messages: true,
	assignedToUser: true,
});

export const SessionBaseSchema = SessionModelSchema.omit({
	user: true,
});

export const AccountPaginatedOutputSchema =
	createPaginatedOutputSchema(AccountBaseSchema);
export const LicensePaginatedOutputSchema =
	createPaginatedOutputSchema(LicenseBaseSchema);
export const DevicePaginatedOutputSchema =
	createPaginatedOutputSchema(DeviceBaseSchema);
export const UserPaginatedOutputSchema =
	createPaginatedOutputSchema(UserBaseSchema);
export const EventPaginatedOutputSchema =
	createPaginatedOutputSchema(EventBaseSchema);
export const DeviceExpansionPaginatedOutputSchema = createPaginatedOutputSchema(
	DeviceExpansionBaseSchema,
);
export const InvitationPaginatedOutputSchema =
	createPaginatedOutputSchema(InvitationBaseSchema);
export const MessagePaginatedOutputSchema =
	createPaginatedOutputSchema(MessageBaseSchema);
export const PaymentIntentPaginatedOutputSchema = createPaginatedOutputSchema(
	PaymentIntentBaseSchema,
);
export const RateLimitPaginatedOutputSchema =
	createPaginatedOutputSchema(RateLimitBaseSchema);
export const RefundRequestPaginatedOutputSchema = createPaginatedOutputSchema(
	RefundRequestBaseSchema,
);
export const AuditLogPaginatedOutputSchema =
	createPaginatedOutputSchema(AuditLogBaseSchema);
export const TicketPaginatedOutputSchema =
	createPaginatedOutputSchema(TicketBaseSchema);
export const SessionPaginatedOutputSchema =
	createPaginatedOutputSchema(SessionBaseSchema);

// types
export type AccountType = z.infer<typeof AccountBaseSchema>;
export type UserLicenseType = z.infer<typeof LicenseBaseSchema>;
export type DeviceType = z.infer<typeof DeviceBaseSchema>;
export type UserType = z.infer<typeof UserBaseSchema>;
export type EventType = z.infer<typeof EventBaseSchema>;
export type DeviceExpansionType = z.infer<typeof DeviceExpansionBaseSchema>;
export type InvitationType = z.infer<typeof InvitationBaseSchema>;
export type MessageType = z.infer<typeof MessageBaseSchema>;
export type PaymentIntentType = z.infer<typeof PaymentIntentBaseSchema>;
export type RateLimitType = z.infer<typeof RateLimitBaseSchema>;
export type RefundRequestType = z.infer<typeof RefundRequestBaseSchema>;
export type AuditLogType = z.infer<typeof AuditLogBaseSchema>;
export type TicketType = z.infer<typeof TicketBaseSchema>;
export type SessionType = z.infer<typeof SessionBaseSchema>;
