import { createFileRoute, Outlet } from "@tanstack/react-router";
import { GalleryVerticalEnd } from "lucide-react";
import { useEffect } from "react";
import Loader from "@/components/loader";
import { authClient } from "@/lib/auth-client";

export const Route = createFileRoute("/auth")({
	component: AuthPageLayout,
});

function AuthPageLayout() {
	const { data: session, isPending } = authClient.useSession();

	const navigate = Route.useNavigate();

	useEffect(() => {
		if (session) {
			navigate({
				to: "/app",
			});
		}
	}, [session, navigate]);

	if (isPending) {
		return <Loader />;
	}

	return (
		<div className="flex flex-col items-center justify-center gap-6 p-6 min-h-svh bg-muted md:p-10">
			<div className="flex flex-col w-full max-w-sm gap-6">
				<a href="/" className="flex items-center self-center gap-2 font-medium">
					<div className="flex items-center justify-center rounded-md size-6 bg-primary text-primary-foreground">
						<GalleryVerticalEnd className="size-4" />
					</div>
					Snapback
				</a>
				<Outlet />
			</div>
		</div>
	);
}
