import type { DeviceType } from "@repo/types";
import { IconCircleCheckFilled, IconCircleXFilled } from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import type { Column, Row, Table } from "@tanstack/react-table";
import { useMemo } from "react";
import CustomTable from "@/components/custom-table";
import { DragHandle } from "@/components/drag-handle";
import TableActions from "@/components/table-actions";
import TableShowHideColumns from "@/components/table-show-hide-columns";
import SortableHeader from "@/components/table-sortable-header";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import useTable from "@/hooks/use-table";
import { orpc } from "@/utils/orpc";
export const Route = createFileRoute("/app/devices")({
	component: DevicesPage,
});

function DevicesPage() {
	const {
		page,
		limit,
		globalFilter,
		rowSelection,
		sorting,
		handlePageSizeChange,
		handleGlobalFilterChange,
		handlePageChange,
		setRowSelection,
		setSorting,
		search,
	} = useTable();
	const devices = useQuery(
		orpc.devices.paginate.queryOptions({
			input: {
				page,
				limit,
				sortBy: sorting[0]?.id,
				sortOrder: sorting[0]?.desc ? "desc" : "asc",
				search: globalFilter ? search.toLowerCase() : globalFilter,
			},
		}),
	);
	const columns = useMemo(
		() => [
			{
				id: "drag",
				header: () => null,
				cell: ({ row }: { row: Row<DeviceType> }) => (
					<DragHandle id={row.original.id} />
				),
			},
			{
				id: "select",
				header: ({ table }: { table: Table<DeviceType> }) => (
					<div className="flex items-center justify-center">
						<Checkbox
							checked={
								table.getIsAllPageRowsSelected() ||
								(table.getIsSomePageRowsSelected() && "indeterminate")
							}
							onCheckedChange={(value) =>
								table.toggleAllPageRowsSelected(!!value)
							}
							aria-label="Select all"
						/>
					</div>
				),
				cell: ({ row }: { row: Row<DeviceType> }) => (
					<div className="flex items-center justify-center">
						<Checkbox
							checked={row.getIsSelected()}
							onCheckedChange={(value) => row.toggleSelected(!!value)}
							aria-label="Select row"
						/>
					</div>
				),
				enableSorting: false,
				enableHiding: false,
			},

			{
				accessorKey: "deviceType",
				enableSorting: true,
				enableHiding: false,
				header: ({ column }: { column: Column<DeviceType> }) => (
					<SortableHeader column={column} label="Device Type" />
				),
			},
			{
				accessorKey: "deviceModel",
				enableSorting: true,
				header: ({ column }: { column: Column<DeviceType> }) => (
					<SortableHeader column={column} label="Device Model" />
				),
			},
			{
				accessorKey: "operatingSystem",
				enableSorting: true,
				header: ({ column }: { column: Column<DeviceType> }) => (
					<SortableHeader column={column} label="Operating System" />
				),
			},
			{
				accessorKey: "architecture",
				enableSorting: true,
				header: ({ column }: { column: Column<DeviceType> }) => (
					<SortableHeader column={column} label="Architecture" />
				),
				cell: ({ row }: { row: Row<DeviceType> }) => (
					<Badge variant="outline" className="px-1.5 text-muted-foreground">
						{row.original.architecture}
					</Badge>
				),
			},
			{
				accessorKey: "screenResolution",
				enableSorting: true,
				header: ({ column }: { column: Column<DeviceType> }) => (
					<SortableHeader column={column} label="Screen Resolution" />
				),
			},
			{
				accessorKey: "status",
				enableSorting: true,
				header: ({ column }: { column: Column<DeviceType> }) => (
					<SortableHeader column={column} label="Status" />
				),
				cell: ({ row }: { row: Row<DeviceType> }) => (
					<Badge variant="outline" className="px-1.5 text-muted-foreground">
						{row.original.status === "ACTIVE" ? (
							<IconCircleCheckFilled className="fill-green-500 dark:fill-green-400" />
						) : (
							<IconCircleXFilled className="fill-red-500 dark:fill-red-400" />
						)}
						{row.original.status}
					</Badge>
				),
			},
			{
				accessorKey: "appVersion",
				enableSorting: true,
				header: ({ column }: { column: Column<DeviceType> }) => (
					<SortableHeader column={column} label="App Version" />
				),
				cell: ({ row }: { row: Row<DeviceType> }) => (
					<Badge variant="outline" className="font-mono text-muted-foreground">
						{row.original.appVersion}
					</Badge>
				),
			},
			{
				id: "actions",
				header: ({ table }: { table: Table<DeviceType> }) => (
					<TableShowHideColumns table={table} />
				),
				cell: () => (
					<TableActions
						onEdit={() => console.log("Edit")}
						onDelete={() => console.log("Delete")}
					/>
				),
			},
		],

		[],
	);

	return (
		<div className="px-4 py-4">
			<h1 className="mb-4 font-bold text-2xl">Devices</h1>
			<CustomTable
				isLoading={devices.isPending}
				initialData={devices.data}
				columns={columns}
				onPageChange={handlePageChange}
				onPageSizeChange={handlePageSizeChange}
				onRowSelectionChange={setRowSelection}
				rowSelection={rowSelection}
				sorting={sorting}
				onSortingChange={setSorting}
				globalFilter={globalFilter}
				globalFilterPlaceholder="Search devices..."
				onGlobalFilterChange={handleGlobalFilterChange}
				onAddClick={() => console.log("Add")}
				addButtonText="Add Device"
			/>
		</div>
	);
}
