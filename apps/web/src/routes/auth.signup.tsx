import { createFileRoute } from "@tanstack/react-router";
import z from "zod";
import { SignUpForm } from "@/components/signup-form";

const searchSchema = z.object({
	token: z.string().optional(),
	email: z.email().optional(),
});
export const Route = createFileRoute("/auth/signup")({
	component: SignUpPage,
	validateSearch: searchSchema,
});

function SignUpPage() {
	const { email, token } = Route.useSearch();

	return <SignUpForm token={token} email={email} />;
}
