import type { EventType } from "@repo/types";
import { IconCircleCheckFilled, IconCircleXFilled } from "@tabler/icons-react";
import { useQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import type { Column, Row, Table } from "@tanstack/react-table";
import { useMemo } from "react";
import CustomTable from "@/components/custom-table";
import { DragHandle } from "@/components/drag-handle";
import TableActions from "@/components/table-actions";
import TableShowHideColumns from "@/components/table-show-hide-columns";
import SortableHeader from "@/components/table-sortable-header";
import { Badge } from "@/components/ui/badge";
import useTable from "@/hooks/use-table";
import { formatDate } from "@/utils/format-utils";
import { orpc } from "@/utils/orpc";
export const Route = createFileRoute("/app/events")({
	component: EventsPage,
});

function EventsPage() {
	const {
		page,
		limit,
		globalFilter,
		rowSelection,
		sorting,
		handlePageChange,
		handlePageSizeChange,
		handleGlobalFilterChange,
		setRowSelection,
		setSorting,
		search,
	} = useTable();
	const events = useQuery(
		orpc.events.paginate.queryOptions({
			input: {
				page,
				limit,
				sortBy: sorting[0]?.id,
				sortOrder: sorting[0]?.desc ? "desc" : "asc",
				search: globalFilter ? search.toLowerCase() : globalFilter,
			},
		}),
	);

	const columns = useMemo(
		() => [
			{
				id: "drag",
				header: () => null,
				cell: ({ row }: { row: Row<EventType> }) => (
					<DragHandle id={row.original.id} />
				),
			},
			{
				accessorKey: "stripeEventId",
				enableSorting: true,
				enableHiding: false,
				header: ({ column }: { column: Column<EventType> }) => (
					<SortableHeader column={column} label="Stripe Event ID" />
				),
				cell: ({ row }: { row: Row<EventType> }) => (
					<span className="rounded-sm bg-muted p-1 font-mono">
						{row.original.stripeEventId}
					</span>
				),
			},
			{
				accessorKey: "eventType",
				enableSorting: true,
				header: ({ column }: { column: Column<EventType> }) => (
					<SortableHeader column={column} label="Event Type" />
				),
			},
			{
				accessorKey: "processed",
				enableSorting: true,
				header: ({ column }: { column: Column<EventType> }) => (
					<SortableHeader column={column} label="Processed" />
				),
				cell: ({ row }: { row: Row<EventType> }) => (
					<Badge variant="outline" className="px-1.5 text-muted-foreground">
						{row.original.processed ? (
							<IconCircleCheckFilled className="fill-green-500 dark:fill-green-400" />
						) : (
							<IconCircleXFilled className="fill-red-500 dark:fill-red-400" />
						)}
						{row.original.processed ? "YES" : "NO"}
					</Badge>
				),
			},
			{
				accessorKey: "processedAt",
				enableSorting: true,
				header: ({ column }: { column: Column<EventType> }) => (
					<SortableHeader column={column} label="Processed At" />
				),
				cell: ({ row }: { row: Row<EventType> }) => (
					<span>
						{row.original.processedAt
							? formatDate(row.original.processedAt)
							: ""}
					</span>
				),
			},
			{
				accessorKey: "errorMessage",
				enableSorting: true,
				header: ({ column }: { column: Column<EventType> }) => (
					<SortableHeader column={column} label="Error Message" />
				),
			},
			{
				accessorKey: "retryCount",
				enableSorting: true,
				header: ({ column }: { column: Column<EventType> }) => (
					<SortableHeader column={column} label="Retry Count" />
				),
			},
			{
				id: "select",
				header: ({ table }: { table: Table<EventType> }) => (
					<TableShowHideColumns table={table} />
				),
				cell: () => (
					<TableActions
						onEdit={() => console.log("Edit")}
						onDelete={() => console.log("Delete")}
					/>
				),
			},
		],
		[],
	);

	return (
		<div className="px-4 py-4">
			<h1 className="mb-4 font-bold text-2xl">Events</h1>
			<CustomTable
				isLoading={events.isPending}
				initialData={events.data}
				columns={columns}
				onPageChange={handlePageChange}
				onPageSizeChange={handlePageSizeChange}
				onRowSelectionChange={setRowSelection}
				rowSelection={rowSelection}
				sorting={sorting}
				onSortingChange={setSorting}
				globalFilter={globalFilter}
				globalFilterPlaceholder="Search events..."
				onGlobalFilterChange={handleGlobalFilterChange}
				onAddClick={() => console.log("Add")}
				addButtonText="Add Event"
			/>
		</div>
	);
}
