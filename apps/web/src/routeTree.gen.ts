/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as AuthRouteImport } from './routes/auth'
import { Route as AppRouteImport } from './routes/app'
import { Route as IndexRouteImport } from './routes/index'
import { Route as AppIndexRouteImport } from './routes/app.index'
import { Route as AuthSignupRouteImport } from './routes/auth.signup'
import { Route as AuthLoginRouteImport } from './routes/auth.login'
import { Route as AppUsersRouteImport } from './routes/app.users'
import { Route as AppPaymentsRouteImport } from './routes/app.payments'
import { Route as AppLicensesRouteImport } from './routes/app.licenses'
import { Route as AppInvitationsRouteImport } from './routes/app.invitations'
import { Route as AppEventsRouteImport } from './routes/app.events'
import { Route as AppDevicesRouteImport } from './routes/app.devices'
import { Route as AppAuditLogsRouteImport } from './routes/app.audit-logs'
import { Route as AppSettingsIndexRouteImport } from './routes/app.settings.index'

const AuthRoute = AuthRouteImport.update({
  id: '/auth',
  path: '/auth',
  getParentRoute: () => rootRouteImport,
} as any)
const AppRoute = AppRouteImport.update({
  id: '/app',
  path: '/app',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const AppIndexRoute = AppIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AppRoute,
} as any)
const AuthSignupRoute = AuthSignupRouteImport.update({
  id: '/signup',
  path: '/signup',
  getParentRoute: () => AuthRoute,
} as any)
const AuthLoginRoute = AuthLoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => AuthRoute,
} as any)
const AppUsersRoute = AppUsersRouteImport.update({
  id: '/users',
  path: '/users',
  getParentRoute: () => AppRoute,
} as any)
const AppPaymentsRoute = AppPaymentsRouteImport.update({
  id: '/payments',
  path: '/payments',
  getParentRoute: () => AppRoute,
} as any)
const AppLicensesRoute = AppLicensesRouteImport.update({
  id: '/licenses',
  path: '/licenses',
  getParentRoute: () => AppRoute,
} as any)
const AppInvitationsRoute = AppInvitationsRouteImport.update({
  id: '/invitations',
  path: '/invitations',
  getParentRoute: () => AppRoute,
} as any)
const AppEventsRoute = AppEventsRouteImport.update({
  id: '/events',
  path: '/events',
  getParentRoute: () => AppRoute,
} as any)
const AppDevicesRoute = AppDevicesRouteImport.update({
  id: '/devices',
  path: '/devices',
  getParentRoute: () => AppRoute,
} as any)
const AppAuditLogsRoute = AppAuditLogsRouteImport.update({
  id: '/audit-logs',
  path: '/audit-logs',
  getParentRoute: () => AppRoute,
} as any)
const AppSettingsIndexRoute = AppSettingsIndexRouteImport.update({
  id: '/settings/',
  path: '/settings/',
  getParentRoute: () => AppRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/app': typeof AppRouteWithChildren
  '/auth': typeof AuthRouteWithChildren
  '/app/audit-logs': typeof AppAuditLogsRoute
  '/app/devices': typeof AppDevicesRoute
  '/app/events': typeof AppEventsRoute
  '/app/invitations': typeof AppInvitationsRoute
  '/app/licenses': typeof AppLicensesRoute
  '/app/payments': typeof AppPaymentsRoute
  '/app/users': typeof AppUsersRoute
  '/auth/login': typeof AuthLoginRoute
  '/auth/signup': typeof AuthSignupRoute
  '/app/': typeof AppIndexRoute
  '/app/settings': typeof AppSettingsIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/auth': typeof AuthRouteWithChildren
  '/app/audit-logs': typeof AppAuditLogsRoute
  '/app/devices': typeof AppDevicesRoute
  '/app/events': typeof AppEventsRoute
  '/app/invitations': typeof AppInvitationsRoute
  '/app/licenses': typeof AppLicensesRoute
  '/app/payments': typeof AppPaymentsRoute
  '/app/users': typeof AppUsersRoute
  '/auth/login': typeof AuthLoginRoute
  '/auth/signup': typeof AuthSignupRoute
  '/app': typeof AppIndexRoute
  '/app/settings': typeof AppSettingsIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/app': typeof AppRouteWithChildren
  '/auth': typeof AuthRouteWithChildren
  '/app/audit-logs': typeof AppAuditLogsRoute
  '/app/devices': typeof AppDevicesRoute
  '/app/events': typeof AppEventsRoute
  '/app/invitations': typeof AppInvitationsRoute
  '/app/licenses': typeof AppLicensesRoute
  '/app/payments': typeof AppPaymentsRoute
  '/app/users': typeof AppUsersRoute
  '/auth/login': typeof AuthLoginRoute
  '/auth/signup': typeof AuthSignupRoute
  '/app/': typeof AppIndexRoute
  '/app/settings/': typeof AppSettingsIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/app'
    | '/auth'
    | '/app/audit-logs'
    | '/app/devices'
    | '/app/events'
    | '/app/invitations'
    | '/app/licenses'
    | '/app/payments'
    | '/app/users'
    | '/auth/login'
    | '/auth/signup'
    | '/app/'
    | '/app/settings'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/auth'
    | '/app/audit-logs'
    | '/app/devices'
    | '/app/events'
    | '/app/invitations'
    | '/app/licenses'
    | '/app/payments'
    | '/app/users'
    | '/auth/login'
    | '/auth/signup'
    | '/app'
    | '/app/settings'
  id:
    | '__root__'
    | '/'
    | '/app'
    | '/auth'
    | '/app/audit-logs'
    | '/app/devices'
    | '/app/events'
    | '/app/invitations'
    | '/app/licenses'
    | '/app/payments'
    | '/app/users'
    | '/auth/login'
    | '/auth/signup'
    | '/app/'
    | '/app/settings/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AppRoute: typeof AppRouteWithChildren
  AuthRoute: typeof AuthRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/auth': {
      id: '/auth'
      path: '/auth'
      fullPath: '/auth'
      preLoaderRoute: typeof AuthRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/app': {
      id: '/app'
      path: '/app'
      fullPath: '/app'
      preLoaderRoute: typeof AppRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/app/': {
      id: '/app/'
      path: '/'
      fullPath: '/app/'
      preLoaderRoute: typeof AppIndexRouteImport
      parentRoute: typeof AppRoute
    }
    '/auth/signup': {
      id: '/auth/signup'
      path: '/signup'
      fullPath: '/auth/signup'
      preLoaderRoute: typeof AuthSignupRouteImport
      parentRoute: typeof AuthRoute
    }
    '/auth/login': {
      id: '/auth/login'
      path: '/login'
      fullPath: '/auth/login'
      preLoaderRoute: typeof AuthLoginRouteImport
      parentRoute: typeof AuthRoute
    }
    '/app/users': {
      id: '/app/users'
      path: '/users'
      fullPath: '/app/users'
      preLoaderRoute: typeof AppUsersRouteImport
      parentRoute: typeof AppRoute
    }
    '/app/payments': {
      id: '/app/payments'
      path: '/payments'
      fullPath: '/app/payments'
      preLoaderRoute: typeof AppPaymentsRouteImport
      parentRoute: typeof AppRoute
    }
    '/app/licenses': {
      id: '/app/licenses'
      path: '/licenses'
      fullPath: '/app/licenses'
      preLoaderRoute: typeof AppLicensesRouteImport
      parentRoute: typeof AppRoute
    }
    '/app/invitations': {
      id: '/app/invitations'
      path: '/invitations'
      fullPath: '/app/invitations'
      preLoaderRoute: typeof AppInvitationsRouteImport
      parentRoute: typeof AppRoute
    }
    '/app/events': {
      id: '/app/events'
      path: '/events'
      fullPath: '/app/events'
      preLoaderRoute: typeof AppEventsRouteImport
      parentRoute: typeof AppRoute
    }
    '/app/devices': {
      id: '/app/devices'
      path: '/devices'
      fullPath: '/app/devices'
      preLoaderRoute: typeof AppDevicesRouteImport
      parentRoute: typeof AppRoute
    }
    '/app/audit-logs': {
      id: '/app/audit-logs'
      path: '/audit-logs'
      fullPath: '/app/audit-logs'
      preLoaderRoute: typeof AppAuditLogsRouteImport
      parentRoute: typeof AppRoute
    }
    '/app/settings/': {
      id: '/app/settings/'
      path: '/settings'
      fullPath: '/app/settings'
      preLoaderRoute: typeof AppSettingsIndexRouteImport
      parentRoute: typeof AppRoute
    }
  }
}

interface AppRouteChildren {
  AppAuditLogsRoute: typeof AppAuditLogsRoute
  AppDevicesRoute: typeof AppDevicesRoute
  AppEventsRoute: typeof AppEventsRoute
  AppInvitationsRoute: typeof AppInvitationsRoute
  AppLicensesRoute: typeof AppLicensesRoute
  AppPaymentsRoute: typeof AppPaymentsRoute
  AppUsersRoute: typeof AppUsersRoute
  AppIndexRoute: typeof AppIndexRoute
  AppSettingsIndexRoute: typeof AppSettingsIndexRoute
}

const AppRouteChildren: AppRouteChildren = {
  AppAuditLogsRoute: AppAuditLogsRoute,
  AppDevicesRoute: AppDevicesRoute,
  AppEventsRoute: AppEventsRoute,
  AppInvitationsRoute: AppInvitationsRoute,
  AppLicensesRoute: AppLicensesRoute,
  AppPaymentsRoute: AppPaymentsRoute,
  AppUsersRoute: AppUsersRoute,
  AppIndexRoute: AppIndexRoute,
  AppSettingsIndexRoute: AppSettingsIndexRoute,
}

const AppRouteWithChildren = AppRoute._addFileChildren(AppRouteChildren)

interface AuthRouteChildren {
  AuthLoginRoute: typeof AuthLoginRoute
  AuthSignupRoute: typeof AuthSignupRoute
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthLoginRoute: AuthLoginRoute,
  AuthSignupRoute: AuthSignupRoute,
}

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AppRoute: AppRouteWithChildren,
  AuthRoute: AuthRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
