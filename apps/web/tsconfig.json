{"compilerOptions": {"strict": true, "esModuleInterop": true, "jsx": "react-jsx", "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "verbatimModuleSyntax": true, "skipLibCheck": true, "types": ["vite/client"], "rootDirs": ["."], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@repo/types": ["../packages/database/src/index.ts"]}}, "references": [{"path": "../server"}]}