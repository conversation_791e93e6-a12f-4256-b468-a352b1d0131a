import { z } from "zod";

export const envSchema = z.object({
	APP_URL: z.url(),
	DATABASE_URL: z.url(),
	CORS_ORIGIN: z.url(),
	BETTER_AUTH_SECRET: z.string().min(1),
	BETTER_AUTH_URL: z.url(),
	STRIPE_SECRET_KEY: z
		.string()
		.min(1)
		.refine((value) => value.startsWith("sk_")),
	STRIPE_PUBLIC_KEY: z
		.string()
		.min(1)
		.refine((value) => value.startsWith("pk_")),
	STRIPE_WEBHOOK_SECRET: z
		.string()
		.min(1)
		.refine((value) => value.startsWith("whsec_")),
	JWT_SECRET: z.string().min(1),
	RESEND_API_KEY: z.string().min(1),
	RESEND_FROM_EMAIL: z.string(),
});

// we need to validate all items are present in .env
export function validateEnv() {
	try {
		envSchema.parse(process.env);
	} catch (error) {
		console.error("Invalid environment variables:", error);
		process.exit(1);
	}
}
