import {
	AccountPaginatedOutputSchema,
	IdInputSchema,
	PaginateInputSchema,
} from "@repo/types";
import prisma from "@/db";

import { protectedProcedure } from "@/lib/orpc";
import { buildPaginationResponse } from "@/lib/pagination";

export const accounts = {
	list: protectedProcedure
		.route({
			method: "GET",
			path: "/accounts",
			summary: "List accounts",
			description: "List accounts",
			tags: ["accounts"],
		})
		.handler(async ({ context }) => {
			return await prisma.account.findMany();
		}),
	paginate: protectedProcedure
		.route({
			method: "GET",
			path: "/accounts",
			summary: "Paginated accounts",
			description: "Paginated accounts",
			tags: ["accounts"],
		})
		.input(PaginateInputSchema)
		.output(AccountPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildPaginationResponse(prisma.account, input);
		}),

	get: protectedProcedure
		.route({
			method: "GET",
			path: "/accounts/{id}",
			summary: "Get account",
			description: "Get account",
			tags: ["accounts"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.account.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
	delete: protectedProcedure
		.route({
			method: "DELETE",
			path: "/accounts/{id}",
			summary: "Delete account",
			description: "Delete account",
			tags: ["accounts"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.account.delete({
				where: {
					id: input.id,
				},
			});
		}),
};
