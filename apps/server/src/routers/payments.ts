import {
	IdInputSchema,
	PaginateInputSchema,
	PaymentIntentPaginatedOutputSchema,
} from "@repo/types";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildPaginationResponse } from "@/lib/pagination";

export const payments = {
	list: protectedProcedure
		.route({
			method: "GET",
			path: "/payments",
			summary: "List payments",
			description: "List payments",
			tags: ["payments"],
		})
		.handler(async ({ context }) => {
			return await prisma.paymentIntent.findMany();
		}),
	paginate: protectedProcedure
		.route({
			method: "GET",
			path: "/payments/paginate",
			summary: "Paginated payments",
			description: "Paginated payments",
			tags: ["payments"],
		})
		.input(PaginateInputSchema)
		.output(PaymentIntentPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildPaginationResponse(prisma.paymentIntent, input);
		}),

	get: protectedProcedure
		.route({
			method: "GET",
			path: "/payments/{id}",
			summary: "Get payment",
			description: "Get payment",
			tags: ["payments"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.paymentIntent.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
};
