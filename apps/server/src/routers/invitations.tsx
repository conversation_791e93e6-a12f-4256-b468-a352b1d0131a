import {
	CreateInvitationInputSchema,
	IdInputSchema,
	InvitationPaginatedOutputSchema,
	PaginateInputSchema,
	TokenInputSchema,
	UpdateInvitationInputSchema,
} from "@repo/types";
import { generateInvitationToken } from "prisma/helpers";
import SendInvitation from "transactional/emails/user/SendInvitation";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildPaginationResponse } from "@/lib/pagination";
import { sendEmail } from "@/lib/resend";

export const invitations = {
	list: protectedProcedure
		.route({
			method: "GET",
			path: "/invitations",
			summary: "List invitations",
			description: "List invitations",
			tags: ["invitations"],
		})
		.handler(async ({ context }) => {
			return await prisma.userInvitation.findMany();
		}),
	paginate: protectedProcedure
		.route({
			method: "GET",
			path: "/invitations/paginate",
			summary: "Paginated invitations",
			description: "Paginated invitations",
			tags: ["invitations"],
		})
		.input(PaginateInputSchema)
		.output(InvitationPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildPaginationResponse(prisma.userInvitation, input);
		}),

	get: protectedProcedure
		.route({
			method: "GET",
			path: "/invitations/{id}",
			summary: "Get invitation",
			description: "Get invitation",
			tags: ["invitations"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.userInvitation.findUnique({
				where: {
					id: input.id,
				},
			});
		}),

	create: protectedProcedure
		.route({
			method: "POST",
			path: "/invitations",
			summary: "Create invitation",
			description: "Create invitation",
			tags: ["invitations"],
		})
		.input(CreateInvitationInputSchema)
		.handler(async ({ input, context }) => {
			const token = generateInvitationToken();
			const sentByUser = context.session.user;
			const invitation = await prisma.userInvitation.create({
				data: {
					email: input.email,
					role: input.role,
					sentBy: sentByUser.id,
					token,
					expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
				},
			});

			const sendInvitationEmailProps = {
				inviter: sentByUser.name,
				inviteeEmail: input.email,
				teamName: "SnapBack",
				acceptUrl: `${process.env.APP_URL}/auth/signup?email=${input.email}&token=${invitation.token}`,
			};

			// Send invitation email
			await sendEmail({
				to: input.email,
				subject: "You're invited to join SnapBack",
				react: <SendInvitation {...sendInvitationEmailProps} />,
				props: sendInvitationEmailProps,
			});

			return invitation;
		}),

	update: protectedProcedure
		.route({
			method: "PUT",
			path: "/invitations",
			summary: "Update invitation",
			description: "Update invitation",
			tags: ["invitations"],
		})
		.input(UpdateInvitationInputSchema)
		.handler(async ({ input }) => {
			return await prisma.userInvitation.update({
				where: {
					id: input.id,
				},
				data: {
					status: input.status,
				},
			});
		}),

	delete: protectedProcedure
		.route({
			method: "DELETE",
			path: "/invitations/{id}",
			summary: "Delete invitation",
			description: "Delete invitation",
			tags: ["invitations"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.userInvitation.delete({
				where: {
					id: input.id,
				},
			});
		}),

	validate: protectedProcedure
		.route({
			method: "GET",
			path: "/invitations/{token}/validate",
			summary: "Validate invitation token",
			description: "Validate invitation token",
			tags: ["invitations"],
		})
		.input(TokenInputSchema)
		.handler(async ({ input }) => {
			return await prisma.userInvitation.findUnique({
				where: {
					token: input.token,
				},
				select: {
					id: true,
					email: true,
					role: true,
					status: true,
				},
			});
		}),
};
