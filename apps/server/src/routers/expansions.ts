import {
	CreateDeviceExpansionInputSchema,
	DeviceExpansionPaginatedOutputSchema,
	IdInputSchema,
	PaginateInputSchema,
	UpdateDeviceExpansionInputSchema,
} from "@repo/types";
import prisma from "@/db";

import { protectedProcedure } from "@/lib/orpc";
import { buildPaginationResponse } from "@/lib/pagination";

export const expansions = {
	list: protectedProcedure
		.route({
			method: "GET",
			path: "/expansions",
			summary: "List expansions",
			description: "List expansions",
			tags: ["expansions"],
		})
		.handler(async ({ context }) => {
			return await prisma.deviceExpansion.findMany();
		}),
	paginate: protectedProcedure
		.route({
			method: "GET",
			path: "/expansions/paginate",
			summary: "Paginated expansions",
			description: "Paginated expansions",
			tags: ["expansions"],
		})
		.input(PaginateInputSchema)
		.output(DeviceExpansionPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildPaginationResponse(prisma.deviceExpansion, input);
		}),

	get: protectedProcedure.input(IdInputSchema).handler(async ({ input }) => {
		return await prisma.deviceExpansion.findUnique({
			where: {
				id: input.id,
			},
		});
	}),
	create: protectedProcedure
		.route({
			method: "POST",
			path: "/expansions",
			summary: "Create expansion",
			description: "Create expansion",
			tags: ["expansions"],
		})
		.input(CreateDeviceExpansionInputSchema)
		.handler(async ({ input }) => {
			return await prisma.deviceExpansion.create({
				data: {
					licenseId: input.licenseId,
					paymentIntentId: input.paymentIntentId,
					additionalDevices: input.additionalDevices,
					amount: input.amount,
				},
			});
		}),

	update: protectedProcedure
		.route({
			method: "PUT",
			path: "/expansions",
			summary: "Update expansion",
			description: "Update expansion",
			tags: ["expansions"],
		})
		.input(UpdateDeviceExpansionInputSchema)
		.handler(async ({ input }) => {
			return await prisma.deviceExpansion.update({
				where: {
					id: input.id,
				},
				data: {
					status: input.status,
					additionalDevices: input.additionalDevices,
					amount: input.amount,
				},
			});
		}),
	delete: protectedProcedure
		.route({
			method: "DELETE",
			path: "/expansions/{id}",
			summary: "Delete expansion",
			description: "Delete expansion",
			tags: ["expansions"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.deviceExpansion.delete({
				where: {
					id: input.id,
				},
			});
		}),
};
