import {
	CreateRefundRequestInputSchema,
	IdInputSchema,
	PaginateInputSchema,
	RefundRequestPaginatedOutputSchema,
	UpdateRefundRequestInputSchema,
} from "@repo/types";

import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildPaginationResponse } from "@/lib/pagination";

export const refunds = {
	list: protectedProcedure
		.route({
			method: "GET",
			path: "/refunds",
			summary: "List refunds",
			description: "List refunds",
			tags: ["refunds"],
		})
		.handler(async ({ context }) => {
			return await prisma.refundRequest.findMany();
		}),
	paginate: protectedProcedure
		.route({
			method: "GET",
			path: "/refunds/paginate",
			summary: "Paginated refunds",
			description: "Paginated refunds",
			tags: ["refunds"],
		})
		.input(PaginateInputSchema)
		.output(RefundRequestPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildPaginationResponse(prisma.refundRequest, input);
		}),

	get: protectedProcedure
		.route({
			method: "GET",
			path: "/refunds/{id}",
			summary: "Get refund",
			description: "Get refund",
			tags: ["refunds"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.refundRequest.findUnique({
				where: {
					id: input.id,
				},
			});
		}),

	create: protectedProcedure
		.route({
			method: "POST",
			path: "/refunds",
			summary: "Create refund",
			description: "Create refund",
			tags: ["refunds"],
		})
		.input(CreateRefundRequestInputSchema)
		.handler(async ({ input }) => {
			return await prisma.refundRequest.create({
				data: {
					licenseId: input.licenseId,
					requestedBy: input.requestedBy,
					reason: input.reason,
				},
			});
		}),

	update: protectedProcedure
		.route({
			method: "PUT",
			path: "/refunds",
			summary: "Update refund",
			description: "Update refund",
			tags: ["refunds"],
		})
		.input(UpdateRefundRequestInputSchema)
		.handler(async ({ input }) => {
			return await prisma.refundRequest.update({
				where: {
					id: input.id,
				},
				data: {
					status: input.status,
					approvedAmount: input.approvedAmount,
					stripeRefundIds: input.stripeRefundIds,
					adminNotes: input.adminNotes,
					processedBy: input.processedBy,
				},
			});
		}),

	delete: protectedProcedure
		.route({
			method: "DELETE",
			path: "/refunds/{id}",
			summary: "Delete refund",
			description: "Delete refund",
			tags: ["refunds"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.refundRequest.delete({
				where: {
					id: input.id,
				},
			});
		}),
};
