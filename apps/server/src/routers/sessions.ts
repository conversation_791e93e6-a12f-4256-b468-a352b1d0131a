import {
	IdInputSchema,
	PaginateInputSchema,
	SessionPaginatedOutputSchema,
} from "@repo/types";

import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildPaginationResponse } from "@/lib/pagination";

export const sessions = {
	list: protectedProcedure
		.route({
			method: "GET",
			path: "/sessions",
			summary: "List sessions",
			description: "List sessions",
			tags: ["sessions"],
		})
		.handler(async ({ context }) => {
			return await prisma.session.findMany();
		}),
	paginate: protectedProcedure
		.route({
			method: "GET",
			path: "/sessions/paginate",
			summary: "Paginated sessions",
			description: "Paginated sessions",
			tags: ["sessions"],
		})
		.input(PaginateInputSchema)
		.output(SessionPaginatedOutputSchema)
		.handler(async ({ input }) => {
			return await buildPaginationResponse(prisma.session, input);
		}),

	get: protectedProcedure
		.route({
			method: "GET",
			path: "/sessions/{id}",
			summary: "Get session",
			description: "Get session",
			tags: ["sessions"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.session.findUnique({
				where: {
					id: input.id,
				},
			});
		}),
	delete: protectedProcedure
		.route({
			method: "DELETE",
			path: "/sessions/{id}",
			summary: "Delete session",
			description: "Delete session",
			tags: ["sessions"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.session.delete({
				where: {
					id: input.id,
				},
			});
		}),
};
