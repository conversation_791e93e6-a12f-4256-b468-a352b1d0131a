import {
	CreateLicenseInputSchema,
	IdInputSchema,
	LicensePaginatedOutputSchema,
	PaginateInputSchema,
	UpdateLicenseInputSchema,
} from "@repo/types";
import prisma from "@/db";
import { protectedProcedure } from "@/lib/orpc";
import { buildPaginationResponse } from "@/lib/pagination";

export const licenses = {
	list: protectedProcedure
		.route({
			method: "GET",
			path: "/licenses",
			summary: "List licenses",
			description: "List licenses",
			tags: ["licenses"],
		})
		.handler(async ({ context }) => {
			return await prisma.license.findMany();
		}),
	paginate: protectedProcedure
		.route({
			method: "GET",
			path: "/licenses/paginate",
			summary: "Paginated licenses",
			description: "Paginated licenses",
			tags: ["licenses"],
		})
		.input(PaginateInputSchema)
		.output(LicensePaginatedOutputSchema)
		.handler(async ({ input }) => {
			const { search, filters } = input;

			let where: Record<string, unknown> = {};

			if (search) {
				where = {
					...where,
					OR: [
						{ customerEmail: { contains: search } },
						{ customerName: { contains: search } },
					],
					...filters,
				};
			}

			return await buildPaginationResponse(prisma.license, input, where);
		}),

	get: protectedProcedure
		.route({
			method: "GET",
			path: "/licenses/{id}",
			summary: "Get license",
			description: "Get license",
			tags: ["licenses"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.license.findUnique({
				where: {
					id: input.id,
				},
			});
		}),

	create: protectedProcedure
		.route({
			method: "POST",
			path: "/licenses",
			summary: "Create license",
			description: "Create license",
			tags: ["licenses"],
		})
		.input(CreateLicenseInputSchema)
		.handler(async ({ input }) => {
			function generateLicenseKey() {
				const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
				let result = "";
				for (let i = 0; i < 24; i++) {
					result += characters.charAt(
						Math.floor(Math.random() * characters.length),
					);
				}
				return result;
			}
			const licenseKey = generateLicenseKey();

			return await prisma.license.create({
				data: {
					customerEmail: input.customerEmail,
					customerName: input.customerName,
					licenseType: input.licenseType,
					licenseKey: licenseKey,
				},
			});
		}),

	update: protectedProcedure
		.route({
			method: "PUT",
			path: "/licenses",
			summary: "Update license",
			description: "Update license",
			tags: ["licenses"],
		})
		.input(UpdateLicenseInputSchema)
		.handler(async ({ input }) => {
			return await prisma.license.update({
				where: {
					id: input.id,
				},
				data: {
					licenseType: input.licenseType,
					status: input.status,
					maxDevices: input.maxDevices,
				},
			});
		}),

	delete: protectedProcedure
		.route({
			method: "DELETE",
			path: "/licenses/{id}",
			summary: "Delete license",
			description: "Delete license",
			tags: ["licenses"],
		})
		.input(IdInputSchema)
		.handler(async ({ input }) => {
			return await prisma.license.delete({
				where: {
					id: input.id,
				},
			});
		}),
};
