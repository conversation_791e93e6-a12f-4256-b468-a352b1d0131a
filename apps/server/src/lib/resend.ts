import { Resend } from "resend";

// if (!process.env.RESEND_API_KEY) {
// 	throw new Error("RESEND_API_KEY is not defined");
// }

export const resend = new Resend(process.env.RESEND_API_KEY);

interface SendEmailOptions {
	from?: string;
	to: string;
	subject: string;
	react: React.ReactNode;
	props?: Record<string, any>;
}

/**
 * Send an email using Resend
 * @param options - Email options
 * @param options.from - Sender email address (must be verified in Resend)
 * @param options.to - Recipient email address
 * @param options.subject - Email subject line
 * @param options.react - Email template component (React Email)
 * @param options.props - Optional props to pass to React component
 * @returns Promise that resolves to the Resend response
 * @throws Error if email send fails
 */
export const sendEmail = ({
	props = {},
	from,
	...options
}: SendEmailOptions) => {
	if (process.env.NODE_ENV !== "production") {
		console.log("[SEND_EMAIL] Skipping email send in dev mode", props);

		return Promise.resolve({
			error: null,
		});
	}

	return resend.emails.send({
		from: from || (process.env.RESEND_FROM_EMAIL as string),
		...options,
	});
};
