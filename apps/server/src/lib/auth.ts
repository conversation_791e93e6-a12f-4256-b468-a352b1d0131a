import { expo } from "@better-auth/expo";

// import { UserRole } from "@repo/types";
import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import prisma from "@/db";

export const auth = betterAuth({
	database: prismaAdapter(prisma, {
		provider: "postgresql",
	}),
	trustedOrigins: [process.env.CORS_ORIGIN || "", "mybettertapp://", "exp://"],
	emailAndPassword: {
		enabled: true,
		autoSignIn: true,
	},
	user: {
		additionalFields: {
			role: {
				type: "string",
				required: false,
				input: false,
			},
			token: {
				type: "string",
				required: false,
				input: true,
			},
		},
	},
	//Database hooks for first user bootstrap and invitation integration
	databaseHooks: {
		user: {
			create: {
				before: async (user, ctx) => {
					// Check if this is the first user (bootstrap admin)
					const userCount = await prisma.user.count();

					if (userCount === 0) {
						return {
							data: {
								...user,
								role: "SUPER_ADMIN",
								isActive: true,
								emailVerified: true, // First user is auto-verified
							},
						};
					}

					// For subsequent users, check if they were already created through invitation acceptance
					const invitation = await prisma.invitation.findUnique({
						where: { token: ctx?.body.token },
					});

					if (!invitation) {
						throw new Error("Invalid invitation token");
					}

					if (invitation?.email !== user.email) {
						// User was created through invitation acceptance - use existing data
						throw new Error("User already exists");
					}

					return {
						data: {
							...user,
							role: invitation?.role || "USER",
							isActive: true,
							emailVerified: true, // Users created through invitation are auto-verified
						},
					};
				},
				after: async (user, ctx) => {
					// create audit log for user creation
					await prisma.auditLog.create({
						data: {
							action: "USER_CREATED",
							userId: user.id,
							userEmail: user.email,
						},
					});

					if (!ctx?.body.token) return;

					// Mark invitation as accepted
					await prisma.invitation.update({
						where: { token: ctx?.body.token },
						data: {
							status: "ACCEPTED",
							acceptedAt: new Date(),
							acceptedBy: user.id,
						},
					});
				},
			},
		},
	},

	advanced: {
		defaultCookieAttributes: {
			sameSite: "none",
			secure: true,
			httpOnly: true,
		},
	},
	plugins: [expo()],
});
