import { isEventProcessed, verifyWebhookSignature } from "@/lib/webhook";
import { webhookProcessor } from "@/services/webhook-processor";

/**
 * Stripe webhook handler
 * Processes incoming Stripe webhook events with signature verification and idempotency checks
 */
export async function handleStripeWebhook(c: any) {
	try {
		// Get the raw body for signature verification
		const body = await c.req.text();
		const signature = c.req.header("stripe-signature");

		if (!signature) {
			console.error("Missing Stripe signature header");
			return c.json({ error: "Missing signature" }, 400);
		}

		const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;
		if (!endpointSecret) {
			console.error("Missing STRIPE_WEBHOOK_SECRET environment variable");
			return c.json({ error: "Webhook not configured" }, 500);
		}

		// Verify the webhook signature
		const event = verifyWebhookSignature(body, signature, endpointSecret);

		// Check if we've already processed this event (idempotency)
		if (await isEventProcessed(event.id)) {
			console.log(`Event ${event.id} already processed, skipping`);
			return c.json({ received: true, message: "Event already processed" });
		}

		// Process the event asynchronously
		// We return success immediately to Stripe, then process in background
		setImmediate(async () => {
			try {
				await webhookProcessor.processEvent(event);
			} catch (error) {
				console.error(
					`Background processing failed for event ${event.id}:`,
					error,
				);
			}
		});

		return c.json({ received: true });
	} catch (error) {
		console.error("Webhook processing error:", error);
		return c.json(
			{ error: error instanceof Error ? error.message : "Unknown error" },
			400,
		);
	}
}
