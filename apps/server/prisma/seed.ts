import { randomBytes } from "node:crypto";
import {
	randAlphaNumeric,
	randBetweenDate,
	randBoolean,
	randChanceBoolean,
	randCity,
	randCountry,
	randEmail,
	randFullName,
	randFutureDate,
	randHex,
	randIp,
	randNumber,
	randPastDate,
	randPhrase,
	randProductName,
	randRecentDate,
	randText,
	randUserAgent,
	randUuid,
	randWord,
} from "@ngneat/falso";
import { PrismaClient } from "./generated/client";
import { generateLicenseKey, hashDeviceId } from "./helpers";

const prisma = new PrismaClient();

// Helper functions

function generateSalt(): string {
	return randomBytes(16).toString("hex");
}

function generateTicketId(index: number): string {
	const year = new Date().getFullYear();
	return `SNAP-${year}-${String(index).padStart(3, "0")}`;
}

// Helper function to get a random hex string without the # prefix
function generateHexToken(length: number): string {
	return randHex({ length: 1 })[0]
		.replace("#", "")
		.repeat(Math.ceil(length / 6))
		.substring(0, length);
}

// Enum values
const LICENSE_TYPES = ["TRIAL", "PRO", "ENTERPRISE"] as const;
const LICENSE_STATUSES = [
	"ACTIVE",
	"EXPIRED",
	"SUSPENDED",
	"REFUNDED",
	"CANCELLED",
] as const;
const DEVICE_STATUSES = ["ACTIVE", "INACTIVE", "REMOVED"] as const;
const PAYMENT_STATUSES = [
	"PENDING",
	"PROCESSING",
	"SUCCEEDED",
	"FAILED",
	"CANCELLED",
	"REFUNDED",
] as const;
const PAYMENT_TYPES = [
	"LICENSE_PURCHASE",
	"DEVICE_EXPANSION",
	"SUBSCRIPTION",
] as const;
const DEVICE_EXPANSION_STATUSES = ["PENDING", "PROCESSED", "FAILED"] as const;
const REFUND_STATUSES = [
	"PENDING",
	"APPROVED",
	"REJECTED",
	"PROCESSED",
	"FAILED",
] as const;
const USER_ROLES = ["ADMIN", "COLLABORATOR", "USER", "VIWER"] as const;
const INVITATION_STATUSES = [
	"PENDING",
	"ACCEPTED",
	"EXPIRED",
	"REVOKED",
] as const;
const TICKET_STATUSES = [
	"OPEN",
	"IN_PROGRESS",
	"WAITING_CUSTOMER",
	"RESOLVED",
	"CLOSED",
] as const;
const TICKET_CATEGORIES = [
	"LICENSE",
	"BILLING",
	"TECHNICAL",
	"GENERAL",
] as const;
const TICKET_PRIORITIES = ["LOW", "MEDIUM", "HIGH", "URGENT"] as const;

const AUDIT_ACTIONS = [
	// License actions
	"LICENSE_CREATED",
	"TRIAL_LICENSE_CREATED",
	"LICENSE_ACTIVATED",
	"LICENSE_VALIDATED",
	"LICENSE_EXPIRED",
	"LICENSE_SUSPENDED",
	"LICENSE_REACTIVATED",
	"LICENSE_DELETED",
	"LICENSE_EXTENDED",
	"LICENSE_CANCELLED",
	"LICENSE_UPGRADED",
	// Device actions
	"DEVICE_REGISTERED",
	"DEVICE_UPDATED",
	"DEVICE_REMOVED",
	"DEVICE_EXPANSION_PURCHASED",
	"DEVICE_EXPANSION_PROCESSED",
	// Payment actions
	"PAYMENT_INITIATED",
	"PAYMENT_SUCCEEDED",
	"PAYMENT_FAILED",
	"PAYMENT_REFUNDED",
	// Refund actions
	"REFUND_REQUESTED",
	"REFUND_APPROVED",
	"REFUND_REJECTED",
	"REFUND_PROCESSED",
	"REFUND_FAILED",
	"REFUND_UPDATED",
	// User management actions
	"USER_CREATED",
	"USER_UPDATED",
	"USER_ROLE_CHANGED",
	"USER_ACTIVATED",
	"USER_DEACTIVATED",
	"USER_SUSPENDED",
	"USER_DELETED",
	"USER_LOGIN",
	"USER_LOGOUT",
	"USER_EMAIL_VERIFIED",
	"FIRST_USER_ADMIN_GRANTED",
	// Invitation actions
	"INVITATION_SENT",
	"INVITATION_ACCEPTED",
	"INVITATION_EXPIRED",
	"INVITATION_REVOKED",
	"INVITATION_RESENT",
	"INVITATION_UPDATED",
	"INVITATION_CANCELLED",
	"USER_CREATED_FROM_INVITATION",
	// Security actions
	"SUSPICIOUS_ACTIVITY_DETECTED",
	"RATE_LIMIT_EXCEEDED",
	"UNAUTHORIZED_ACCESS_ATTEMPT",
	"MULTIPLE_DEVICE_LIMIT_EXCEEDED",
	// System actions
	"WEBHOOK_PROCESSED",
	"WEBHOOK_FAILED",
	"DATA_EXPORT_REQUESTED",
	"DATA_DELETED",
	// Email & Communication actions
	"EMAIL_SENT",
	"BULK_EMAIL_SENT",
	"EMAIL_DELIVERY_FAILED",
	"EMAIL_TEMPLATE_UPDATED",
	// Notification actions
	"NOTIFICATION_CREATED",
	"NOTIFICATIONS_MARKED_READ",
	"NOTIFICATION_SETTINGS_UPDATED",
	// Support actions
	"SUPPORT_TICKET_CREATED",
	"SUPPORT_TICKET_UPDATED",
	"SUPPORT_TICKET_RESOLVED",
	"SUPPORT_MESSAGE_SENT",
] as const;

const DEVICE_TYPES = ["Desktop", "Laptop", "Mobile", "Tablet", "Server"];
const DEVICE_MODELS = [
	"MacBook Pro",
	"iMac",
	"Windows PC",
	"iPhone",
	"iPad",
	"Android Phone",
	"Surface Pro",
];
const OPERATING_SYSTEMS = [
	"macOS",
	"Windows 11",
	"Windows 10",
	"iOS",
	"Android",
	"Linux",
];
const ARCHITECTURES = ["x64", "arm64", "x86"];

async function main() {
	console.log("🌱 Starting database seeding...");

	// Clear existing data
	console.log("🧹 Clearing existing data...");
	await prisma.supportMessage.deleteMany();
	await prisma.supportTicket.deleteMany();
	await prisma.auditLog.deleteMany();
	await prisma.rateLimit.deleteMany();
	await prisma.refundRequest.deleteMany();
	await prisma.device.deleteMany();
	await prisma.deviceExpansion.deleteMany();
	await prisma.license.deleteMany();
	await prisma.webhookEvent.deleteMany();
	await prisma.paymentIntent.deleteMany();
	await prisma.invitation.deleteMany();
	await prisma.session.deleteMany();
	await prisma.account.deleteMany();
	await prisma.verification.deleteMany();
	await prisma.user.deleteMany();

	console.log("👥 Creating admin users...");

	// Create admin users
	const adminUsers = [];
	for (let i = 0; i < 5; i++) {
		const user = await prisma.user.create({
			data: {
				name: randFullName(),
				email: randEmail(),
				emailVerified: randBoolean(),
				image: randBoolean() ? `https://avatar.vercel.sh/${randWord()}` : null,
				role: USER_ROLES[randNumber({ min: 0, max: USER_ROLES.length - 1 })],
				isActive: randChanceBoolean({ chanceTrue: 0.9 }),
				lastLoginAt: randChanceBoolean({ chanceTrue: 0.8 })
					? randRecentDate()
					: null,
			},
		});
		adminUsers.push(user);
	}

	console.log("💳 Creating payment intents...");

	// Create payment intents
	const paymentIntents = [];
	for (let i = 0; i < 30; i++) {
		const paymentIntent = await prisma.paymentIntent.create({
			data: {
				stripePaymentIntentId: `pi_${randAlphaNumeric({ length: 24 }).join("")}`,
				stripeCheckoutSessionId: randChanceBoolean({ chanceTrue: 0.7 })
					? `cs_${randAlphaNumeric({ length: 24 }).join("")}`
					: null,
				amount: randNumber({ min: 2999, max: 19999 }), // $29.99 to $199.99
				currency: "usd",
				status:
					PAYMENT_STATUSES[
						randNumber({ min: 0, max: PAYMENT_STATUSES.length - 1 })
					],
				paymentType:
					PAYMENT_TYPES[randNumber({ min: 0, max: PAYMENT_TYPES.length - 1 })],
				customerEmail: randEmail(),
				customerName: randChanceBoolean({ chanceTrue: 0.8 })
					? randFullName()
					: null,
				processedAt: randChanceBoolean({ chanceTrue: 0.8 })
					? randRecentDate()
					: null,
			},
		});
		paymentIntents.push(paymentIntent);
	}

	console.log("🔑 Creating licenses...");

	// Create licenses
	const licenses = [];
	for (let i = 0; i < 25; i++) {
		const paymentIntent =
			paymentIntents[randNumber({ min: 0, max: paymentIntents.length - 1 })];
		const createdBy = randChanceBoolean({ chanceTrue: 0.7 })
			? adminUsers[randNumber({ min: 0, max: adminUsers.length - 1 })].id
			: null;

		const license = await prisma.license.create({
			data: {
				licenseKey: generateLicenseKey(),
				licenseType:
					LICENSE_TYPES[randNumber({ min: 0, max: LICENSE_TYPES.length - 1 })],
				status:
					LICENSE_STATUSES[
						randNumber({ min: 0, max: LICENSE_STATUSES.length - 1 })
					],
				maxDevices: randNumber({ min: 1, max: 10 }),
				usedDevices: randNumber({ min: 0, max: 5 }),
				expiresAt: randChanceBoolean({ chanceTrue: 0.8 })
					? randFutureDate()
					: null,
				activatedAt: randChanceBoolean({ chanceTrue: 0.9 })
					? randPastDate()
					: null,
				customerEmail: paymentIntent.customerEmail,
				customerName: paymentIntent.customerName,
				createdBy,
				paymentIntentId: paymentIntent.id,
				totalPaidAmount: paymentIntent.amount,
				refundedAt: randChanceBoolean({ chanceTrue: 0.1 })
					? randPastDate()
					: null,
				refundReason: randChanceBoolean({ chanceTrue: 0.1 })
					? randPhrase()
					: null,
				refundAmount: randChanceBoolean({ chanceTrue: 0.1 })
					? randNumber({ min: 1000, max: paymentIntent.amount })
					: null,
				emailSentAt: randChanceBoolean({ chanceTrue: 0.95 })
					? randRecentDate()
					: null,
				emailDeliveryStatus: randChanceBoolean({ chanceTrue: 0.95 })
					? "delivered"
					: "failed",
			},
		});
		licenses.push(license);
	}

	console.log("📱 Creating devices...");

	// Create devices for licenses
	const devices = [];
	for (const license of licenses) {
		const deviceCount = randNumber({
			min: 0,
			max: Math.min(license.maxDevices, 3),
		});

		for (let i = 0; i < deviceCount; i++) {
			const salt = generateSalt();
			const deviceId = randUuid();
			const { hash: deviceHash, salt: deviceSalt } = hashDeviceId(
				deviceId,
				salt,
			);

			const device = await prisma.device.create({
				data: {
					licenseId: license.id,
					deviceHash,
					salt: deviceSalt,
					status:
						DEVICE_STATUSES[
							randNumber({ min: 0, max: DEVICE_STATUSES.length - 1 })
						],
					lastSeen: randRecentDate(),
					removedAt: randChanceBoolean({ chanceTrue: 0.1 })
						? randPastDate()
						: null,
					appVersion: `${randNumber({ min: 1, max: 3 })}.${randNumber({ min: 0, max: 9 })}.${randNumber({ min: 0, max: 9 })}`,
					deviceName: randProductName(),
					deviceType:
						DEVICE_TYPES[randNumber({ min: 0, max: DEVICE_TYPES.length - 1 })],
					deviceModel:
						DEVICE_MODELS[
							randNumber({ min: 0, max: DEVICE_MODELS.length - 1 })
						],
					operatingSystem:
						OPERATING_SYSTEMS[
							randNumber({ min: 0, max: OPERATING_SYSTEMS.length - 1 })
						],
					architecture:
						ARCHITECTURES[
							randNumber({ min: 0, max: ARCHITECTURES.length - 1 })
						],
					screenResolution: `${randNumber({ min: 1920, max: 3840 })}x${randNumber({ min: 1080, max: 2160 })}`,
					totalMemory: `${randNumber({ min: 8, max: 64 })}GB`,
					userNickname: randChanceBoolean({ chanceTrue: 0.3 })
						? randWord()
						: null,
					location: randChanceBoolean({ chanceTrue: 0.4 })
						? `${randCity()}, ${randCountry()}`
						: null,
					notes: randChanceBoolean({ chanceTrue: 0.2 }) ? randText() : null,
				},
			});
			devices.push(device);
		}
	}

	console.log("🔧 Creating device expansions...");

	// Create device expansions
	const deviceExpansions = [];
	for (let i = 0; i < 8; i++) {
		const license = licenses[randNumber({ min: 0, max: licenses.length - 1 })];
		const paymentIntent =
			paymentIntents[randNumber({ min: 0, max: paymentIntents.length - 1 })];

		const deviceExpansion = await prisma.deviceExpansion.create({
			data: {
				licenseId: license.id,
				paymentIntentId: paymentIntent.id,
				additionalDevices: randNumber({ min: 1, max: 5 }),
				amount: randNumber({ min: 999, max: 4999 }), // $9.99 to $49.99
				status:
					DEVICE_EXPANSION_STATUSES[
						randNumber({ min: 0, max: DEVICE_EXPANSION_STATUSES.length - 1 })
					],
				processedAt: randChanceBoolean({ chanceTrue: 0.8 })
					? randRecentDate()
					: null,
			},
		});
		deviceExpansions.push(deviceExpansion);
	}

	console.log("💸 Creating refund requests...");

	// Create refund requests
	const refundRequests = [];
	for (let i = 0; i < 6; i++) {
		const license = licenses[randNumber({ min: 0, max: licenses.length - 1 })];
		const processedBy = randChanceBoolean({ chanceTrue: 0.7 })
			? adminUsers[randNumber({ min: 0, max: adminUsers.length - 1 })].id
			: null;

		const refundRequest = await prisma.refundRequest.create({
			data: {
				licenseId: license.id,
				requestedBy: license.customerEmail,
				reason: randPhrase(),
				status:
					REFUND_STATUSES[
						randNumber({ min: 0, max: REFUND_STATUSES.length - 1 })
					],
				requestedAmount: randNumber({
					min: 1000,
					max: license.totalPaidAmount,
				}),
				approvedAmount: randChanceBoolean({ chanceTrue: 0.6 })
					? randNumber({ min: 1000, max: license.totalPaidAmount })
					: null,
				stripeRefundIds: randChanceBoolean({ chanceTrue: 0.5 })
					? [`re_${randAlphaNumeric({ length: 24 }).join("")}`]
					: [],
				adminNotes: randChanceBoolean({ chanceTrue: 0.6 }) ? randText() : null,
				processedBy,
				processedAt: processedBy ? randRecentDate() : null,
			},
		});
		refundRequests.push(refundRequest);
	}

	console.log("🎫 Creating support tickets...");

	// Create support tickets
	const supportTickets = [];
	for (let i = 0; i < 15; i++) {
		const assignedTo = randChanceBoolean({ chanceTrue: 0.6 })
			? adminUsers[randNumber({ min: 0, max: adminUsers.length - 1 })].id
			: null;
		const license = randChanceBoolean({ chanceTrue: 0.7 })
			? licenses[randNumber({ min: 0, max: licenses.length - 1 })]
			: null;

		const supportTicket = await prisma.supportTicket.create({
			data: {
				ticketId: generateTicketId(i + 1),
				subject: randPhrase(),
				description: randText(),
				status:
					TICKET_STATUSES[
						randNumber({ min: 0, max: TICKET_STATUSES.length - 1 })
					],
				priority:
					TICKET_PRIORITIES[
						randNumber({ min: 0, max: TICKET_PRIORITIES.length - 1 })
					],
				category:
					TICKET_CATEGORIES[
						randNumber({ min: 0, max: TICKET_CATEGORIES.length - 1 })
					],
				customerEmail: license?.customerEmail || randEmail(),
				customerName: license?.customerName || randFullName(),
				licenseKey: license?.licenseKey || null,
				assignedTo,
				resolvedAt: randChanceBoolean({ chanceTrue: 0.4 })
					? randRecentDate()
					: null,
			},
		});
		supportTickets.push(supportTicket);
	}

	console.log("💬 Creating support messages...");

	// Create support messages
	const supportMessages = [];
	for (const ticket of supportTickets) {
		const messageCount = randNumber({ min: 1, max: 5 });

		for (let i = 0; i < messageCount; i++) {
			const isFromAdmin = randChanceBoolean({ chanceTrue: 0.5 });

			const supportMessage = await prisma.supportMessage.create({
				data: {
					ticketId: ticket.id,
					message: randText(),
					isInternal: randChanceBoolean({ chanceTrue: 0.2 }),
					authorEmail: isFromAdmin ? null : ticket.customerEmail,
					authorId: isFromAdmin
						? adminUsers[randNumber({ min: 0, max: adminUsers.length - 1 })].id
						: null,
					createdAt: randBetweenDate({
						from: ticket.createdAt,
						to: new Date(),
					}),
				},
			});
			supportMessages.push(supportMessage);
		}
	}

	console.log("📊 Creating audit logs...");

	// Create audit logs
	const auditLogs = [];
	for (let i = 0; i < 50; i++) {
		const user = randChanceBoolean({ chanceTrue: 0.8 })
			? adminUsers[randNumber({ min: 0, max: adminUsers.length - 1 })]
			: null;
		const license = randChanceBoolean({ chanceTrue: 0.6 })
			? licenses[randNumber({ min: 0, max: licenses.length - 1 })]
			: null;
		const device =
			randChanceBoolean({ chanceTrue: 0.3 }) && devices.length > 0
				? devices[randNumber({ min: 0, max: devices.length - 1 })]
				: null;

		const auditLog = await prisma.auditLog.create({
			data: {
				action:
					AUDIT_ACTIONS[randNumber({ min: 0, max: AUDIT_ACTIONS.length - 1 })],
				licenseKey: license?.licenseKey || null,
				deviceHash: device?.deviceHash || null,
				licenseId: license?.id || null,
				deviceId: device?.id || null,
				userId: user?.id || null,
				userEmail: user?.email || null,
				targetId: randChanceBoolean({ chanceTrue: 0.2 })
					? adminUsers[randNumber({ min: 0, max: adminUsers.length - 1 })].id
					: null,
				customerEmail: license?.customerEmail || null,
				ipAddress: randIp(),
				userAgent: randUserAgent(),
				details: randChanceBoolean({ chanceTrue: 0.4 })
					? { additionalInfo: randText() }
					: undefined,
			},
		});
		auditLogs.push(auditLog);
	}

	console.log("🚦 Creating rate limits...");

	// Create rate limits
	const rateLimits = [];
	for (let i = 0; i < 10; i++) {
		const rateLimit = await prisma.rateLimit.create({
			data: {
				identifier: randBoolean() ? randIp() : randEmail(),
				action: ["license_validation", "device_registration", "api_request"][
					randNumber({ min: 0, max: 2 })
				],
				count: randNumber({ min: 1, max: 100 }),
				windowStart: randRecentDate(),
				expiresAt: randFutureDate(),
			},
		});
		rateLimits.push(rateLimit);
	}

	console.log("🔗 Creating webhook events...");

	// Create webhook events
	const webhookEvents = [];
	for (let i = 0; i < 20; i++) {
		const paymentIntent = randChanceBoolean({ chanceTrue: 0.8 })
			? paymentIntents[randNumber({ min: 0, max: paymentIntents.length - 1 })]
			: null;

		const webhookEvent = await prisma.webhookEvent.create({
			data: {
				stripeEventId: `evt_${randAlphaNumeric({ length: 24 }).join("")}`,
				eventType: [
					"payment_intent.succeeded",
					"payment_intent.payment_failed",
					"checkout.session.completed",
				][randNumber({ min: 0, max: 2 })],
				processed: randChanceBoolean({ chanceTrue: 0.9 }),
				processedAt: randChanceBoolean({ chanceTrue: 0.9 })
					? randRecentDate()
					: null,
				errorMessage: randChanceBoolean({ chanceTrue: 0.1 })
					? randText()
					: null,
				retryCount: randNumber({ min: 0, max: 3 }),
				paymentIntentId: paymentIntent?.id || null,
			},
		});
		webhookEvents.push(webhookEvent);
	}

	console.log("📧 Creating user invitations...");

	// Create user invitations
	const userInvitations = [];
	for (let i = 0; i < 8; i++) {
		const sentBy =
			adminUsers[randNumber({ min: 0, max: adminUsers.length - 1 })];
		const acceptedBy = randChanceBoolean({ chanceTrue: 0.6 })
			? adminUsers[randNumber({ min: 0, max: adminUsers.length - 1 })]
			: null;

		const userInvitation = await prisma.invitation.create({
			data: {
				email: randEmail(),
				role: USER_ROLES[randNumber({ min: 0, max: USER_ROLES.length - 1 })],
				token: generateHexToken(32),
				status:
					INVITATION_STATUSES[
						randNumber({ min: 0, max: INVITATION_STATUSES.length - 1 })
					],
				expiresAt: randFutureDate(),
				acceptedAt: acceptedBy ? randRecentDate() : null,
				sentBy: sentBy.id,
				acceptedBy: acceptedBy?.id || null,
			},
		});
		userInvitations.push(userInvitation);
	}

	console.log("✅ Seeding completed successfully!");
	console.log("Created:");
	console.log(`- ${adminUsers.length} admin users`);
	console.log(`- ${paymentIntents.length} payment intents`);
	console.log(`- ${licenses.length} licenses`);
	console.log(`- ${devices.length} devices`);
	console.log(`- ${deviceExpansions.length} device expansions`);
	console.log(`- ${refundRequests.length} refund requests`);
	console.log(`- ${supportTickets.length} support tickets`);
	console.log(`- ${supportMessages.length} support messages`);
	console.log(`- ${auditLogs.length} audit logs`);
	console.log(`- ${rateLimits.length} rate limits`);
	console.log(`- ${webhookEvents.length} webhook events`);
	console.log(`- ${userInvitations.length} user invitations`);
}

main()
	.catch((e) => {
		console.error("❌ Seeding failed:", e);
		process.exit(1);
	})
	.finally(async () => {
		await prisma.$disconnect();
	});
