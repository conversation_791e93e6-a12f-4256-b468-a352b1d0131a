model User {
  id            String    @id @default(cuid())
  name          String
  email         String    @unique
  emailVerified Boolean   @default(false)
  image         String?
  role          UserRole  @default(USER)
  isActive      Boolean   @default(true)
  invitedBy     String? // ID of user who invited this user
  invitedAt     DateTime? // When the user was invited
  lastLoginAt   DateTime? // Track last login for security
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Better-auth relationships
  sessions Session[]
  accounts Account[]

  // License management relationships
  createdLicenses     License[]       @relation("LicenseCreatedBy")
  sentInvitations     Invitation[]    @relation("InvitationSentBy")
  receivedInvitations Invitation[]    @relation("InvitationReceivedBy")
  auditLogsAsActor    AuditLog[]      @relation("AuditLogActor")
  auditLogsAsTarget   AuditLog[]      @relation("AuditLogTarget")
  processedRefunds    RefundRequest[] @relation("RefundProcessedBy")

  // Support system relationships
  assignedTickets SupportTicket[]  @relation("TicketAssignedTo")
  supportMessages SupportMessage[] @relation("MessageAuthor")

  @@index([role])
  @@index([isActive])
  @@index([email])
  @@map("users")
}

model Session {
  id        String   @id @default(cuid())
  expiresAt DateTime
  token     String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model Account {
  id                    String    @id @default(cuid())
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  @@map("accounts")
}

model Verification {
  id         String   @id @default(cuid())
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@map("verifications")
}

model Invitation {
  id         String           @id @default(cuid())
  email      String
  role       UserRole
  token      String           @unique
  status     InvitationStatus @default(PENDING)
  expiresAt  DateTime
  sentAt     DateTime         @default(now())
  acceptedAt DateTime?

  // Relationships
  sentBy         String
  sentByUser     User    @relation("InvitationSentBy", fields: [sentBy], references: [id], onDelete: Cascade)
  acceptedBy     String?
  acceptedByUser User?   @relation("InvitationReceivedBy", fields: [acceptedBy], references: [id])

  @@index([email])
  @@index([token])
  @@index([status])
  @@index([expiresAt])
  @@map("invitations")
}

enum UserRole {
  SUPER_ADMIN
  ADMIN
  COLLABORATOR
  USER
  VIWER
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  EXPIRED
  REVOKED
}
