import crypto from "node:crypto";
import jwt from "jsonwebtoken";

/**
 * Hash a device ID using PBKDF2 with a salt for secure storage
 *
 * @param {string} deviceId - The device identifier to hash
 * @param {string|null} salt - Optional salt to use. If null, generates a new random salt
 * @returns {Object} Object containing the hash and salt used
 * @returns {string} returns.hash - The hashed device ID
 * @returns {string} returns.salt - The salt used for hashing
 *
 * @example
 * // Generate new hash with random salt
 * const { hash, salt } = hashDeviceId("device123");
 *
 * @example
 * // Hash with existing salt for verification
 * const { hash } = hashDeviceId("device123", existingSalt);
 */
const hashDeviceId = (deviceId: string, salt: string | null = null) => {
	const actualSalt = salt || crypto.randomBytes(16).toString("hex");
	const hash = crypto
		.pbkdf2Sync(deviceId, actualSalt, 10000, 64, "sha512")
		.toString("hex");
	return { hash, salt: actualSalt };
};

/**
 * Verify a JWT device token and return its payload
 *
 * @param {string} token - The JWT token to verify
 * @returns {Object|null} The decoded token payload or null if invalid
 * @returns {string} returns.licenseId - License ID from the token
 * @returns {string} returns.deviceHash - Device hash from the token
 *
 * @example
 * const payload = verifyDeviceToken(token);
 * if (payload) {
 *   console.log(`License: ${payload.licenseId}, Device: ${payload.deviceHash}`);
 * }
 */
const verifyDeviceToken = (token: string) => {
	try {
		return jwt.verify(token, process.env.JWT_SECRET as string);
	} catch (_error) {
		return null;
	}
};

/**
 * Generate a JWT token for device authentication
 *
 * @param {string} licenseId - The license ID to include in the token
 * @param {string} deviceHash - The hashed device ID to include in the token
 * @returns {string} JWT token valid for 365 days
 *
 * @example
 * const token = generateDeviceToken("license123", "hashedDevice456");
 * // Use token for device authentication
 */
const generateDeviceToken = (licenseId: string, deviceHash: string) => {
	return jwt.sign({ licenseId, deviceHash }, process.env.JWT_SECRET as string, {
		expiresIn: "365d",
	});
};

/**
 * Generate a cryptographically secure 24-character alphanumeric license key
 *
 * This function creates license keys that are:
 * - 24 characters long (divisible by 4 for XXXX-XXXX-XXXX-XXXX-XXXX-XXXX format)
 * - Alphanumeric (A-Z, 0-9) for maximum entropy
 * - Cryptographically secure and cannot be reverse engineered
 * - Uses multiple entropy sources and mixing techniques
 * - Excludes ambiguous characters (0, O, 1, I, L) for better user experience
 *
 * @returns {string} A 24-character secure alphanumeric license key
 *
 * @example
 * const licenseKey = generateLicenseKey();
 * console.log(licenseKey); // "A7K9M2P5Q8R3T6V9W4X7Y2Z5"
 */
const generateLicenseKey = (): string => {
	// Character set excluding ambiguous characters (0, O, 1, I, L)
	// This gives us 31 characters total (26 letters - 3 + 10 digits - 2)
	const charset = "ABCDEFGHJKMNPQRSTUVWXYZ23456789";
	const charsetLength = charset.length;

	// Generate multiple entropy sources
	const timestamp = Date.now().toString(36).toUpperCase();
	const randomBytes1 = crypto.randomBytes(16);
	const randomBytes2 = crypto.randomBytes(16);
	const randomBytes3 = crypto.randomBytes(8);

	// Create a mixed entropy pool
	const entropyPool = Buffer.concat([
		randomBytes1,
		Buffer.from(timestamp, "utf8"),
		randomBytes2,
		randomBytes3,
	]);

	// Generate license key using secure random selection
	let licenseKey = "";
	for (let i = 0; i < 24; i++) {
		// Use multiple bytes for each character selection to increase security
		const randomIndex1 = entropyPool[i % entropyPool.length];
		const randomIndex2 = entropyPool[(i + 13) % entropyPool.length];
		const randomIndex3 = crypto.randomBytes(1)[0];

		// Combine multiple entropy sources
		const combinedIndex =
			(randomIndex1 ^ randomIndex2 ^ randomIndex3) % charsetLength;
		licenseKey += charset[combinedIndex];
	}

	// Additional security: shuffle the result using Fisher-Yates with crypto random
	const keyArray = licenseKey.split("");
	for (let i = keyArray.length - 1; i > 0; i--) {
		const randomBytes = crypto.randomBytes(4);
		const randomIndex = randomBytes.readUInt32BE(0) % (i + 1);
		[keyArray[i], keyArray[randomIndex]] = [keyArray[randomIndex], keyArray[i]];
	}

	return keyArray.join("");
};

const generateInvitationToken = (): string => {
	return crypto.randomBytes(32).toString("hex");
};

export {
	hashDeviceId,
	verifyDeviceToken,
	generateDeviceToken,
	generateLicenseKey,
	generateInvitationToken,
};
