# SnapBack License Management API Routes





## �🔐 Authentication & User Management ✅

### Auth Routes (Better-Auth)

- [ ] `GET /api/auth/session` - Get current session
- [ ] `POST /api/auth/forgot-password` - Send password reset email
- [ ] `POST /api/auth/reset-password` - Reset password with token

### Admin User Management
- [x] `GET /api/admin/users/me` - Get current user details
- [x] `GET /api/admin/users` - List all admin users (with pagination/filters)
- [ ] `GET /api/admin/users/:id` - Get specific admin user
- [ ] `PATCH /api/admin/users/:id` - Update admin user (role, status, etc.)
- [x] `DELETE /api/admin/users/:id` - Deactivate admin user
- [ ] `POST /api/admin/users/:id/reactivate` - Reactivate admin user

### User Invitations
- [x] `GET /api/admin/invitations` - List all pending invitations
- [x] `GET /api/invitations/:token/validate` - Validate invitation token (public)
- [x] `POST /api/admin/invitations` - Send invitation to new admin
- [x] `POST /api/admin/invitations/:token/accept` - Accept invitation (deprecated) better-auth handles this now
- [ ] `DELETE /api/admin/invitations/:id` - Revoke invitation
- [ ] `POST /api/admin/invitations/:id/resend` - Resend invitation email
- [ ] `PATCH /api/admin/invitations/:id` - Update invitation details

---

## 💳 Payment & Billing

### Stripe Integration
- [ ] `POST /api/payments/create-checkout` - Create Stripe checkout session for license purchase
- [ ] `POST /api/payments/create-expansion-checkout` - Create checkout for device expansion
- [ ] `POST /api/payments/webhooks/stripe` - Handle Stripe webhooks (license creation, refunds)
- [ ] `GET /api/admin/payments/:paymentIntentId` - Get payment details(admin only )
- [ ] `GET /api/admin/payments` - List all payments (admin only, with filters)

### Webhook Management ✅
- [ ] `GET /api/admin/webhooks` - List webhook events (with filters) ✅
- [ ] `POST /api/admin/webhooks/:id/retry` - Retry failed webhook processing ✅
- [ ] `GET /api/admin/webhooks/stats` - Webhook processing statistics ✅

---

## 📄 License Management

### License Operations (Admin)
- [ ] `GET /api/admin/licenses` - List all licenses (with pagination/filters)
- [ ] `GET /api/admin/licenses/:id` - Get specific license details
- [ ] `POST /api/admin/licenses` - Manually create license (admin only)
- [ ] `PATCH /api/admin/licenses/:id` - Update license (extend expiry, change status, etc.)
- [ ] `DELETE /api/admin/licenses/:id` - Cancel/deactivate license
- [ ] `POST /api/admin/licenses/:id/extend` - Extend license expiry
- [ ] `POST /api/admin/licenses/:id/suspend` - Suspend license
- [ ] `POST /api/admin/licenses/:id/reactivate` - Reactivate suspended license

### License Delivery
- [ ] `POST /api/admin/licenses/:email/resend-email` - Resend license key to customer
- [ ] `GET /api/admin/licenses/delivery-status` - Check email delivery status
- [ ] `POST /api/licenses/lookup` - Look up license by customer email (for lost keys)

### License Validation (Public - for App)
- [ ] `POST /api/licenses/validate` - Validate license key and register/update device
- [ ] `POST /api/licenses/ping` - Regular ping from app to check license status
- [ ] `GET /api/licenses/:licenseKey/status` - Get license status (for app)

---

## 📱 Device Management

### Device Operations (Admin)
- [ ] `GET /api/admin/devices` - List all devices (with pagination/filters)
- [ ] `GET /api/admin/devices/:id` - Get specific device details
- [ ] `DELETE /api/admin/devices/:id` - Remove device from license
- [ ] `PATCH /api/admin/devices/:id` - Update device metadata

### Device Operations (App/Customer)
- [ ] `POST /api/devices/register` - Register new device with license
- [ ] `PATCH /api/devices/:deviceHash/update` - Update device info (app version, etc.)
- [ ] `DELETE /api/devices/:deviceHash/remove` - Remove device (from app)
- [ ] `POST /api/devices/heartbeat` - Device heartbeat/ping

### Device Expansion
- [ ] `POST /api/devices/expand` - Purchase additional device slots
- [ ] `GET /api/admin/device-expansions` - List all device expansions
- [ ] `POST /api/admin/device-expansions/:id/process` - Manually process expansion

---

## 💰 Refund Management

### Refund Operations (Customer)
- [ ] `POST /api/refunds/request` - Request refund (public, requires license key)
- [ ] `GET /api/refunds/status/:requestId` - Check refund status

### Refund Operations (Admin)
- [ ] `GET /api/admin/refunds` - List all refund requests (with filters)
- [ ] `GET /api/admin/refunds/:id` - Get specific refund request
- [ ] `POST /api/admin/refunds/:id/approve` - Approve refund request
- [ ] `POST /api/admin/refunds/:id/reject` - Reject refund request
- [ ] `POST /api/admin/refunds/:id/process` - Process approved refund via Stripe
- [ ] `PATCH /api/admin/refunds/:id` - Update refund request (add admin notes)

---

## 📊 Analytics & Reporting ✅

### Dashboard ✅
- [ ] `GET /api/admin/dashboard/stats` - Get dashboard overview statistics ✅
- [ ] `GET /api/admin/dashboard/revenue` - Revenue analytics (monthly/yearly) ✅
- [ ] `GET /api/admin/dashboard/licenses` - License analytics (growth, types, etc.) ✅
- [ ] `GET /api/admin/dashboard/devices` - Device analytics (registrations, active devices) ✅

### Reports ✅
- [ ] `GET /api/admin/reports/licenses` - License report (CSV export) ✅
- [ ] `GET /api/admin/reports/revenue` - Revenue report (CSV export) ✅
- [ ] `GET /api/admin/reports/devices` - Device usage report ✅
- [ ] `GET /api/admin/reports/refunds` - Refunds report ✅

---

## 🔍 Audit & Monitoring

### Audit Logs
- [ ] `GET /api/admin/audit-logs` - List audit logs (with filters)
- [ ] `GET /api/admin/audit-logs/:id` - Get specific audit log
- [ ] `POST /api/admin/audit-logs/export` - Export audit logs (CSV)

### Security & Monitoring
- [ ] `GET /api/admin/security/suspicious-activity` - List suspicious activities
- [ ] `GET /api/admin/security/rate-limits` - View rate limit violations
- [ ] `POST /api/admin/security/blacklist-ip` - Block IP address
- [ ] `GET /api/admin/security/active-sessions` - View active admin sessions

---

## 🔧 System & Utilities

### System Health
- [ ] `GET /api/health` - System health check
- [ ] `GET /api/admin/system/status` - Detailed system status
- [ ] `GET /api/admin/system/metrics` - System metrics (DB connections, etc.)

### Utilities
- [ ] `POST /api/admin/utils/generate-license-key` - Generate new license key
- [ ] `POST /api/admin/utils/validate-email` - Validate email deliverability
- [ ] `GET /api/admin/utils/stripe-status` - Check Stripe connection status

---

## 🔌 Public API (For Website Integration)

### License Purchase Flow
- [ ] `GET /api/public/pricing` - Get current pricing information
- [ ] `POST /api/public/purchase/validate` - Validate purchase details before checkout
- [ ] `GET /api/public/purchase/success` - Handle successful purchase redirect
- [ ] `GET /api/public/purchase/cancel` - Handle cancelled purchase

### Customer Support ✅
- [ ] `POST /api/public/support/license-lookup` - Look up license by email (rate limited)
- [ ] `POST /api/public/support/contact` - Send support message ✅
- [ ] `GET /api/public/support/faq` - Get FAQ data ✅

---

## 📧 Email & Notifications ✅

### Email Operations (Admin) ✅
- [ ] `GET /api/admin/emails/templates` - List email templates ✅
- [ ] `POST /api/admin/emails/send-bulk` - Send bulk emails to customers ✅
- [ ] `GET /api/admin/emails/delivery-stats` - Email delivery statistics ✅

### Notification Management ✅
- [ ] `GET /api/admin/notifications` - List system notifications ✅
- [ ] `POST /api/admin/notifications/mark-read` - Mark notifications as read ✅
- [ ] `GET /api/admin/notifications/settings` - Get notification preferences ✅
- [ ] `PATCH /api/admin/notifications/settings` - Update notification preferences ✅

---

## 🛠️ Implementation Notes

### Authentication Middleware
- All `/api/admin/*` routes require admin authentication
- Role-based access control (SUPER_ADMIN, ADMIN, MANAGER, etc.)
- Rate limiting on all public endpoints

### Common Query Parameters
- `page`, `limit` - Pagination
- `sortBy`, `sortOrder` - Sorting
- `search` - Text search where applicable
- Various filters based on entity type

### Response Format
All endpoints should return standardized `ApiResponse<T>` format:
```json
{
  "success": true,
  "data": { ... },
  "message": "Optional success message"
}
```

### Error Handling
- Proper HTTP status codes
- Consistent error response format
- Detailed error messages for validation failures
- Audit logging for all admin actions

### Security Considerations
- Input validation using Zod schemas
- SQL injection protection via Prisma
- Rate limiting on sensitive endpoints
- IP-based blocking for suspicious activity
- Secure session management via better-auth